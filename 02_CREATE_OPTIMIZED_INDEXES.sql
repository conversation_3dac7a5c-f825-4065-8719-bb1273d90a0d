-- =====================================================
-- GymKod Pro - Optimized Index'ler Script'i
-- Tarih: 2025-06-27
-- Ama�: Frontend API �a�r�lar�na g�re optimize edilmi� index'ler
-- =====================================================

USE [GymProject]
GO

PRINT 'Optimized index''ler olu�turuluyor...'
GO

-- =====================================================
-- 1. MEMBERS TABLOSU - EN KR�T�K (�ok s�k kullan�l�yor)
-- =====================================================

-- �ye arama paneli i�in (Name, PhoneNumber arama + CompanyID + Gender filtresi)
CREATE NONCLUSTERED INDEX IX_Members_CompanyID_IsActive_Name_Gender
ON Members(CompanyID, IsActive, Name, Gender)
INCLUDE (MemberID, PhoneNumber, Email, Balance, ScanNumber, BirthDate, CreationDate);
GO

-- QR kod tarama i�in (Mobil app + Desktop)
CREATE UNIQUE NONCLUSTERED INDEX IX_Members_ScanNumber_CompanyID
ON Members(ScanNumber, CompanyID)
WHERE ScanNumber IS NOT NULL
INCLUDE (MemberID, Name, IsActive);
GO

-- Telefon numaras� arama i�in
CREATE NONCLUSTERED INDEX IX_Members_PhoneNumber_CompanyID
ON Members(PhoneNumber, CompanyID)
INCLUDE (MemberID, Name, ScanNumber, IsActive);
GO

-- Do�um g�n� sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Members_BirthDate_CompanyID_IsActive
ON Members(BirthDate, CompanyID, IsActive)
WHERE BirthDate IS NOT NULL
INCLUDE (MemberID, Name, PhoneNumber);
GO

-- =====================================================
-- 2. PAYMENTS TABLOSU - �OK KR�T�K (�deme ge�mi�i)
-- =====================================================

-- �deme ge�mi�i paneli i�in (Tarih s�ral� + CompanyID)
CREATE NONCLUSTERED INDEX IX_Payments_CompanyID_PaymentDate_IsActive
ON Payments(CompanyID, PaymentDate DESC, IsActive)
INCLUDE (PaymentID, MemberShipID, PaymentAmount, PaymentMethod, OriginalPaymentMethod, PaymentStatus);
GO

-- �yelik bazl� �demeler i�in (JOIN optimizasyonu)
CREATE NONCLUSTERED INDEX IX_Payments_MemberShipID_CompanyID_IsActive
ON Payments(MemberShipID, CompanyID, IsActive)
INCLUDE (PaymentID, PaymentDate, PaymentAmount, PaymentMethod);
GO

-- �deme durumu sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Payments_PaymentStatus_CompanyID
ON Payments(PaymentStatus, CompanyID)
WHERE PaymentStatus IS NOT NULL
INCLUDE (PaymentID, MemberShipID, PaymentDate, PaymentAmount);
GO

-- Ayl�k gelir raporlar� i�in
CREATE NONCLUSTERED INDEX IX_Payments_CompanyID_PaymentDate_Method
ON Payments(CompanyID, PaymentDate, OriginalPaymentMethod)
WHERE IsActive = 1 AND OriginalPaymentMethod != 'Bor�'
INCLUDE (PaymentAmount);
GO

-- =====================================================
-- 3. MEMBERSHIPS TABLOSU - �OK KR�T�K (�yelik y�netimi)
-- =====================================================

-- Aktif �yelikler i�in (En �ok kullan�lan sorgu)
CREATE NONCLUSTERED INDEX IX_Memberships_CompanyID_IsActive_EndDate
ON Memberships(CompanyID, IsActive, EndDate DESC)
INCLUDE (MembershipID, MemberID, MembershipTypeID, StartDate, IsFrozen);
GO

-- �ye bazl� �yelikler i�in
CREATE NONCLUSTERED INDEX IX_Memberships_MemberID_CompanyID_IsActive
ON Memberships(MemberID, CompanyID, IsActive)
INCLUDE (MembershipID, MembershipTypeID, StartDate, EndDate, IsFrozen);
GO

-- �yelik t�r� bazl� sorgular i�in
CREATE NONCLUSTERED INDEX IX_Memberships_MembershipTypeID_CompanyID_IsActive
ON Memberships(MembershipTypeID, CompanyID, IsActive)
INCLUDE (MemberID, StartDate, EndDate);
GO

-- Dondurulan �yelikler i�in
CREATE NONCLUSTERED INDEX IX_Memberships_IsFrozen_CompanyID_IsActive
ON Memberships(IsFrozen, CompanyID, IsActive)
WHERE IsFrozen = 1
INCLUDE (MembershipID, MemberID, StartDate, EndDate);
GO

-- =====================================================
-- 4. ENTRYEXITHISTORIES TABLOSU - KR�T�K (Giri�-��k��)
-- =====================================================

-- G�nl�k giri� raporlar� i�in (En �ok kullan�lan)
CREATE NONCLUSTERED INDEX IX_EntryExitHistories_CompanyID_EntryDate
ON EntryExitHistories(CompanyID, EntryDate DESC)
WHERE EntryDate IS NOT NULL
INCLUDE (EntryExitID, MembershipID, ExitDate, IsActive);
GO

-- �yelik bazl� giri� ge�mi�i i�in
CREATE NONCLUSTERED INDEX IX_EntryExitHistories_MembershipID_CompanyID_EntryDate
ON EntryExitHistories(MembershipID, CompanyID, EntryDate DESC)
INCLUDE (EntryExitID, ExitDate, IsActive);
GO

-- Son 5 saat i�indeki giri�ler i�in (Dashboard)
CREATE NONCLUSTERED INDEX IX_EntryExitHistories_EntryDate_IsActive_CompanyID
ON EntryExitHistories(EntryDate DESC, IsActive, CompanyID)
WHERE EntryDate IS NOT NULL AND IsActive = 1;
GO

-- =====================================================
-- 5. PRODUCTS TABLOSU - ORTA (�r�n y�netimi)
-- =====================================================

-- �r�n listesi paneli i�in
CREATE NONCLUSTERED INDEX IX_Products_CompanyID_IsActive_Name
ON Products(CompanyID, IsActive, Name)
INCLUDE (ProductID, Price, CreationDate);
GO

-- Fiyat aral��� sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Products_CompanyID_Price_IsActive
ON Products(CompanyID, Price, IsActive)
INCLUDE (ProductID, Name, CreationDate);
GO

-- =====================================================
-- 6. TRANSACTIONS TABLOSU - ORTA (��lem ge�mi�i)
-- =====================================================

-- ��lem ge�mi�i i�in
CREATE NONCLUSTERED INDEX IX_Transactions_CompanyID_TransactionDate_IsActive
ON Transactions(CompanyID, TransactionDate DESC, IsActive)
INCLUDE (TransactionID, MemberID, ProductID, TotalPrice, TransactionType, IsPaid);
GO

-- �ye bazl� i�lemler i�in
CREATE NONCLUSTERED INDEX IX_Transactions_MemberID_CompanyID_IsActive
ON Transactions(MemberID, CompanyID, IsActive)
INCLUDE (TransactionID, TransactionDate, TotalPrice, IsPaid);
GO

-- �denmemi� i�lemler i�in
CREATE NONCLUSTERED INDEX IX_Transactions_IsPaid_CompanyID_IsActive
ON Transactions(IsPaid, CompanyID, IsActive)
WHERE IsPaid = 0
INCLUDE (TransactionID, MemberID, TransactionDate, TotalPrice);
GO

-- =====================================================
-- 7. EXPENSES TABLOSU - ORTA (Gider y�netimi)
-- =====================================================

-- Gider listesi paneli i�in
CREATE NONCLUSTERED INDEX IX_Expenses_CompanyID_ExpenseDate_IsActive
ON Expenses(CompanyID, ExpenseDate DESC, IsActive)
INCLUDE (ExpenseID, Description, Amount, ExpenseType, CreationDate);
GO

-- Gider t�r� bazl� sorgular i�in
CREATE NONCLUSTERED INDEX IX_Expenses_ExpenseType_CompanyID_IsActive
ON Expenses(ExpenseType, CompanyID, IsActive)
WHERE ExpenseType IS NOT NULL
INCLUDE (ExpenseID, ExpenseDate, Amount);
GO

-- Tutar aral��� sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Expenses_CompanyID_Amount_IsActive
ON Expenses(CompanyID, Amount, IsActive)
INCLUDE (ExpenseID, ExpenseDate, ExpenseType);
GO

-- =====================================================
-- 8. MEMBERSHIPTYPE TABLOSU - D���K (Referans tablo)
-- =====================================================

-- �yelik t�r� sorgular� i�in
CREATE NONCLUSTERED INDEX IX_MembershipTypes_CompanyID_IsActive
ON MembershipTypes(CompanyID, IsActive)
INCLUDE (MembershipTypeID, TypeName, Branch, Price, Duration);
GO

-- Branch bazl� sorgular i�in
CREATE NONCLUSTERED INDEX IX_MembershipTypes_Branch_CompanyID_IsActive
ON MembershipTypes(Branch, CompanyID, IsActive)
WHERE Branch IS NOT NULL
INCLUDE (MembershipTypeID, TypeName, Price);
GO

-- =====================================================
-- 9. USERS TABLOSU - D���K (Kullan�c� y�netimi)
-- =====================================================

-- Login i�in
CREATE NONCLUSTERED INDEX IX_Users_Email_IsActive
ON Users(Email, IsActive)
INCLUDE (UserID, FirstName, LastName, PasswordHash, PasswordSalt, RequirePasswordChange);
GO

-- Profil foto�raf� sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Users_ProfileImagePath
ON Users(ProfileImagePath)
WHERE ProfileImagePath IS NOT NULL
INCLUDE (UserID, FirstName, LastName);
GO

-- =====================================================
-- 10. COMPANIES TABLOSU - D���K (�irket y�netimi)
-- =====================================================

-- Aktif �irketler i�in
CREATE NONCLUSTERED INDEX IX_Companies_IsActive_CreationDate
ON Companies(IsActive, CreationDate DESC)
INCLUDE (CompanyID, CompanyName, Email, PhoneNumber);
GO

-- =====================================================
-- 11. CITIES & TOWNS TABLOSU - �OK D���K (Referans)
-- =====================================================

-- �ehir sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Cities_CityName
ON Cities(CityName)
INCLUDE (CityID);
GO

-- �l�e sorgular� i�in
CREATE NONCLUSTERED INDEX IX_Towns_CityID_TownName
ON Towns(CityID, TownName)
INCLUDE (TownID);
GO

-- =====================================================
-- 12. WORKOUT PROGRAM SYSTEM INDEXES
-- =====================================================

-- Antrenman program� �ablonlar� i�in
CREATE NONCLUSTERED INDEX IX_WorkoutProgramTemplates_CompanyID_IsActive
ON WorkoutProgramTemplates(CompanyID, IsActive)
INCLUDE (WorkoutProgramTemplateID, ProgramName, ExperienceLevel, TargetGoal, CreationDate);
GO

-- �ye program atamalar� i�in
CREATE NONCLUSTERED INDEX IX_MemberWorkoutPrograms_MemberID_CompanyID_IsActive
ON MemberWorkoutPrograms(MemberID, CompanyID, IsActive)
INCLUDE (MemberWorkoutProgramID, WorkoutProgramTemplateID, StartDate, EndDate);
GO

-- =====================================================
-- 13. EXERCISE SYSTEM INDEXES
-- =====================================================

-- Sistem egzersizleri i�in
CREATE NONCLUSTERED INDEX IX_SystemExercises_ExerciseCategoryID_IsActive
ON SystemExercises(ExerciseCategoryID, IsActive)
INCLUDE (SystemExerciseID, ExerciseName, DifficultyLevel, Equipment);
GO

-- �irket egzersizleri i�in
CREATE NONCLUSTERED INDEX IX_CompanyExercises_CompanyID_ExerciseCategoryID_IsActive
ON CompanyExercises(CompanyID, ExerciseCategoryID, IsActive)
INCLUDE (CompanyExerciseID, ExerciseName, DifficultyLevel);
GO

-- =====================================================
-- 14. REMAINING DEBTS & DEBT PAYMENTS
-- =====================================================

-- Kalan bor�lar i�in
CREATE NONCLUSTERED INDEX IX_RemainingDebts_PaymentID_CompanyID_IsActive
ON RemainingDebts(PaymentID, CompanyID, IsActive)
INCLUDE (RemainingDebtID, RemainingAmount, LastUpdateDate);
GO

-- =====================================================
-- 15. USER OPERATION CLAIMS (Yetkilendirme)
-- =====================================================

-- Kullan�c� yetkileri i�in
CREATE NONCLUSTERED INDEX IX_UserOperationClaims_UserID_IsActive
ON UserOperationClaims(UserID, IsActive)
INCLUDE (UserOperationClaimID, OperationClaimID);
GO

PRINT 'Optimized index''ler ba�ar�yla olu�turuldu!'
PRINT 'Toplam 35+ index olu�turuldu.'
PRINT 'Sistem performans� �nemli �l��de artacakt�r.'
GO
