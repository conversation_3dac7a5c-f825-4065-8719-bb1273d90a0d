import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { FormBuilder, FormGroup } from '@angular/forms';
import { UserLicenseService } from '../../services/user-license.service';
import { DialogService } from '../../services/dialog.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { PaginatedUserLicenseDto } from '../../models/PaginatedUserLicenseDto';
import { LicensePurchaseComponent } from '../license-purchase/license-purchase.component';
import { ExtendLicenseComponent } from '../extend-license/extend-license.component';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-user-licenses-list',
  templateUrl: './user-licenses-list.component.html',
  styleUrls: ['./user-licenses-list.component.css'],
  standalone:false
})
export class UserLicensesListComponent implements OnInit {
  userLicenses: UserLicenseDto[] = [];
  paginationData: PaginatedUserLicenseDto | null = null;
  isLoading = false;
  isApplyingFilter = false;
  displayedColumns: string[] = ['companyName', 'userName', 'packageName', 'role', 'startDate', 'endDate', 'remainingDays', 'actions'];

  // Pagination
  currentPage = 1;
  pageSize = 20;

  // Filtering and Search
  filterForm: FormGroup;
  sortOptions = [
    { value: 'newest', label: 'En Yeni Eklenen' },
    { value: 'oldest', label: 'En Eski Eklenen' },
    { value: 'expiring', label: 'Süresi Yakında Dolan' },
    { value: 'company', label: 'Şirket Adına Göre' }
  ];

  constructor(
    private userLicenseService: UserLicenseService,
    private dialogService: DialogService,
    private dialog: MatDialog,
    private toastr: ToastrService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      searchTerm: [''],
      sortBy: ['newest'],
      companyName: [''],
      remainingDaysMin: [null],
      remainingDaysMax: [null]
    });
  }

  ngOnInit(): void {
    console.log('UserLicensesListComponent initialized'); // Debug log
    this.loadUserLicenses();
    this.setupFormSubscriptions();
  }

  setupFormSubscriptions(): void {
    // Search term değişikliklerini dinle
    this.filterForm.get('searchTerm')?.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.currentPage = 1;
        this.loadUserLicenses();
      });

    // Diğer form değişikliklerini dinle
    this.filterForm.get('sortBy')?.valueChanges.subscribe(() => {
      this.currentPage = 1;
      this.loadUserLicenses();
    });

    this.filterForm.get('companyName')?.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.currentPage = 1;
        this.loadUserLicenses();
      });
  }

  loadUserLicenses(): void {
    console.log('loadUserLicenses called'); // Debug log
    this.isLoading = true;

    const formValue = this.filterForm?.value || {};

    // Şimdi yeni pagination API'yi deneyelim
    this.userLicenseService.getAllPaginated(
      this.currentPage,
      this.pageSize,
      formValue.searchTerm || '',
      formValue.sortBy || 'newest',
      formValue.companyName || '',
      formValue.remainingDaysMin,
      formValue.remainingDaysMax
    ).subscribe({
      next: (response) => {
        console.log('Paginated API Response:', response); // Debug log
        this.paginationData = response.data;
        this.userLicenses = response.data.data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Paginated API Error:', error); // Debug log
        // Eğer yeni API çalışmazsa eski API'ye geri dön
        console.log('Falling back to old API...');
        this.userLicenseService.getAll().subscribe({
          next: (response) => {
            console.log('Fallback API Response:', response); // Debug log
            this.userLicenses = response.data;
            this.isLoading = false;

            // Fake pagination data oluşturalım
            this.paginationData = {
              data: response.data,
              totalCount: response.data.length,
              pageNumber: 1,
              pageSize: 20,
              totalPages: Math.ceil(response.data.length / 20),
              hasPreviousPage: false,
              hasNextPage: false
            };
          },
          error: (fallbackError) => {
            console.error('Fallback API Error:', fallbackError);
            this.toastr.error('Kullanıcı lisansları yüklenirken bir hata oluştu', 'Hata');
            this.isLoading = false;
          }
        });
      }
    });
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadUserLicenses();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1;
    this.loadUserLicenses();
  }

  clearFilters(): void {
    this.filterForm.reset({
      searchTerm: '',
      sortBy: 'newest',
      companyName: '',
      remainingDaysMin: null,
      remainingDaysMax: null
    });
    this.currentPage = 1;
    this.loadUserLicenses();
  }

  hasActiveFilters(): boolean {
    const formValue = this.filterForm?.value || {};
    return !!(
      (formValue.searchTerm && formValue.searchTerm.trim()) ||
      (formValue.companyName && formValue.companyName.trim()) ||
      formValue.remainingDaysMin !== null ||
      formValue.remainingDaysMax !== null ||
      (formValue.sortBy && formValue.sortBy !== 'newest')
    );
  }

  applyRemainingDaysFilter(): void {
    if (this.isApplyingFilter) {
      return; // Prevent spam clicking
    }

    this.isApplyingFilter = true;
    this.currentPage = 1;
    this.loadUserLicenses();

    // Reset the flag after a short delay to prevent rapid clicking
    setTimeout(() => {
      this.isApplyingFilter = false;
    }, 1000);
  }

  openPurchaseDialog(): void {
    const dialogRef = this.dialog.open(LicensePurchaseComponent, {
      width: '500px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      disableClose: false,
      panelClass: 'modern-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUserLicenses();
      }
    });
  }

  openExtendDialog(userLicense: UserLicenseDto): void {
    const dialogRef = this.dialog.open(ExtendLicenseComponent, {
      width: '600px',
      maxWidth: '95vw',
      data: { userLicense },
      disableClose: false,
      panelClass: 'modern-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUserLicenses();
      }
    });
  }

  revokeLicense(id: number, userName: string): void {
    this.dialogService.confirmRevoke(userName).subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.userLicenseService.revokeLicense(id).subscribe({
          next: (response) => {
            this.toastr.success(response.message, 'Başarılı');
            this.loadUserLicenses();
          },
          error: (error) => {
            this.toastr.error('Lisans iptal edilirken bir hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  getRemainingDaysClass(days: number): string {
    if (days <= 0) {
      return 'modern-badge-danger';
    } else if (days <= 7) {
      return 'modern-badge-warning';
    } else if (days <= 30) {
      return 'modern-badge-warning';
    } else {
      return 'modern-badge-success';
    }
  }

  getRemainingDaysBadgeClass(remainingDays: number): string {
    if (remainingDays <= 7) {
      return 'badge bg-danger';
    } else if (remainingDays <= 30) {
      return 'badge bg-warning text-dark';
    } else {
      return 'badge bg-success';
    }
  }

  getPageNumbers(): number[] {
    if (!this.paginationData) return [];

    const totalPages = this.paginationData.totalPages;
    const currentPage = this.paginationData.pageNumber;
    const pages: number[] = [];

    // Show max 5 page numbers
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  trackByLicenseId(index: number, item: UserLicenseDto): number {
    return item.userLicenseID;
  }
}
